"""
HDLYU3 指标测试文件
"""

import pandas as pd
import numpy as np
from hdly_indicator import HDLYIndicator

try:
    import matplotlib.pyplot as plt
    HAS_MATPLOTLIB = True
except ImportError:
    HAS_MATPLOTLIB = False


def test_hdly_indicator():
    """测试HDLYU3指标"""
    print("开始测试HDLYU3指标...")
    
    # 创建测试数据
    dates = pd.date_range('2023-01-01', periods=200, freq='D')
    np.random.seed(42)
    
    # 生成更真实的股价数据
    base_price = 100
    price_changes = np.random.randn(200) * 0.02  # 2%的日波动
    close_prices = [base_price]
    
    for change in price_changes[1:]:
        new_price = close_prices[-1] * (1 + change)
        close_prices.append(max(new_price, 1))  # 确保价格不为负
    
    close_prices = np.array(close_prices)
    
    # 生成OHLC数据
    high_prices = close_prices * (1 + np.random.rand(200) * 0.03)  # 最高价比收盘价高0-3%
    low_prices = close_prices * (1 - np.random.rand(200) * 0.03)   # 最低价比收盘价低0-3%
    open_prices = close_prices + np.random.randn(200) * 0.5
    volumes = np.random.randint(10000, 100000, 200)
    
    # 确保OHLC逻辑正确
    for i in range(len(close_prices)):
        high_prices[i] = max(high_prices[i], close_prices[i], open_prices[i])
        low_prices[i] = min(low_prices[i], close_prices[i], open_prices[i])
    
    df = pd.DataFrame({
        'date': dates,
        'open': open_prices,
        'high': high_prices,
        'low': low_prices,
        'close': close_prices,
        'volume': volumes
    })
    
    print(f"测试数据创建完成，共{len(df)}条记录")
    print("数据样本:")
    print(df.head())
    
    # 计算指标
    try:
        indicator = HDLYIndicator()
        result = indicator.calculate(df)
        print("\n指标计算成功!")
        
        # 显示计算结果
        print("\n最近10天的指标值:")
        display_cols = ['date', 'low', 'VARC', 'VARD', 'BIG_MONEY1', 'COLOR_CONDITION']
        print(result[display_cols].tail(10).to_string(index=False))
        
        # 统计信息
        print(f"\nBIG_MONEY1 统计信息:")
        print(f"最小值: {result['BIG_MONEY1'].min():.4f}")
        print(f"最大值: {result['BIG_MONEY1'].max():.4f}")
        print(f"平均值: {result['BIG_MONEY1'].mean():.4f}")
        print(f"标准差: {result['BIG_MONEY1'].std():.4f}")
        
        # 检查是否有异常值
        nan_count = result['BIG_MONEY1'].isna().sum()
        inf_count = np.isinf(result['BIG_MONEY1']).sum()
        print(f"\nNaN值数量: {nan_count}")
        print(f"无穷值数量: {inf_count}")
        
        return result
        
    except Exception as e:
        print(f"指标计算失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def plot_results(result):
    """绘制结果图表"""
    if result is None:
        print("没有结果数据可以绘制")
        return

    if not HAS_MATPLOTLIB:
        print("matplotlib未安装，跳过绘图")
        return

    try:
        # 创建图表
        fig, axes = plt.subplots(3, 1, figsize=(12, 10))
        
        # 绘制价格图
        axes[0].plot(result['date'], result['close'], label='收盘价', color='blue')
        axes[0].plot(result['date'], result['low'], label='最低价', color='green', alpha=0.7)
        axes[0].set_title('股价走势')
        axes[0].set_ylabel('价格')
        axes[0].legend()
        axes[0].grid(True, alpha=0.3)
        
        # 绘制VARC指标
        axes[1].plot(result['date'], result['VARC'], label='VARC', color='orange')
        axes[1].plot(result['date'], result['VARD'], label='VARD', color='red')
        axes[1].set_title('VARC和VARD指标')
        axes[1].set_ylabel('指标值')
        axes[1].legend()
        axes[1].grid(True, alpha=0.3)
        
        # 绘制BIG_MONEY1指标
        colors = ['red' if cond else 'green' for cond in result['COLOR_CONDITION']]
        axes[2].bar(result['date'], result['BIG_MONEY1'], color=colors, alpha=0.7, width=1)
        axes[2].set_title('BIG_MONEY1 指标 (红色=上涨趋势, 绿色=下跌趋势)')
        axes[2].set_ylabel('BIG_MONEY1')
        axes[2].set_xlabel('日期')
        axes[2].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.xticks(rotation=45)
        plt.show()
        
        print("图表绘制完成")
        
    except Exception as e:
        print(f"绘图失败: {e}")


def test_edge_cases():
    """测试边界情况"""
    print("\n测试边界情况...")
    
    indicator = HDLYIndicator()
    
    # 测试1: 最小数据集
    try:
        small_df = pd.DataFrame({
            'open': [100, 101, 102],
            'high': [101, 102, 103],
            'low': [99, 100, 101],
            'close': [100.5, 101.5, 102.5],
            'volume': [1000, 1100, 1200]
        })
        result = indicator.calculate(small_df)
        print("✓ 最小数据集测试通过")
    except Exception as e:
        print(f"✗ 最小数据集测试失败: {e}")
    
    # 测试2: 相同价格
    try:
        same_price_df = pd.DataFrame({
            'open': [100] * 50,
            'high': [100] * 50,
            'low': [100] * 50,
            'close': [100] * 50,
            'volume': [1000] * 50
        })
        result = indicator.calculate(same_price_df)
        print("✓ 相同价格测试通过")
    except Exception as e:
        print(f"✗ 相同价格测试失败: {e}")


if __name__ == "__main__":
    # 运行测试
    result = test_hdly_indicator()
    
    # 测试边界情况
    test_edge_cases()
    
    # 绘制图表 (需要matplotlib)
    try:
        plot_results(result)
    except ImportError:
        print("\n注意: 需要安装matplotlib来绘制图表")
        print("运行: pip install matplotlib")
    except Exception as e:
        print(f"绘图时出现错误: {e}")
