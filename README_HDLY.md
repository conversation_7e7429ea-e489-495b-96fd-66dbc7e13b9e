# HDLYU3 指标 Python 实现

这是基于 Pine Script 的 HDLYU3 指标的 Python 实现，用于分析股票市场中的大资金流向。

## 功能特点

- **大资金流向分析**: 通过分析价格波动和成交量来识别大资金的进出
- **自定义SMA算法**: 实现了与Pine Script兼容的SMA计算方法
- **信号生成**: 提供买入/卖出信号判断
- **回测功能**: 包含简单的策略回测框架

## 文件说明

- `hdly_indicator.py`: 核心指标实现类
- `test_hdly_indicator.py`: 测试文件，验证指标计算正确性
- `hdly_usage_example.py`: 使用示例，展示如何在交易策略中应用
- `README_HDLY.md`: 本说明文件

## 安装依赖

```bash
pip install pandas numpy
# 可选：用于绘图
pip install matplotlib
```

## 快速开始

### 基本使用

```python
from hdly_indicator import HDLYIndicator
import pandas as pd

# 准备数据 (需要包含 open, high, low, close, volume 列)
df = pd.DataFrame({
    'open': [...],
    'high': [...],
    'low': [...],
    'close': [...],
    'volume': [...]
})

# 计算指标
indicator = HDLYIndicator()
result = indicator.calculate(df)

# 查看结果
print(result[['BIG_MONEY1', 'COLOR_CONDITION']].tail())
```

### 运行测试

```bash
python test_hdly_indicator.py
```

### 运行完整示例

```bash
python hdly_usage_example.py
```

## 指标说明

### 核心指标

1. **VARC**: 价格变化率指标
   - 计算公式: `SMA(abs(low-low[1]), 3, 1) / SMA(max(low-low[1], 0), 3, 1) * 100`

2. **VARD**: VARC的指数移动平均
   - 计算公式: `EMA(VARC * 10, 3)`

3. **BIG_MONEY1**: 主要的大资金指标 (0-100)
   - 当价格接近30日最低点时激活
   - 数值越高表示大资金流入越强

4. **COLOR_CONDITION**: 趋势方向判断
   - True: 上涨趋势 (红色)
   - False: 下跌趋势 (绿色)

### 信号解读

- **买入信号**: BIG_MONEY1 > 80分位数 且 呈上升趋势
- **卖出信号**: BIG_MONEY1 < 20分位数 且 呈下降趋势
- **持有信号**: 其他情况

## 原理说明

该指标基于以下理论：

1. **价格敏感性分析**: 通过分析最低价的变化来捕捉市场敏感度
2. **大资金识别**: 当价格接近历史低点时，大资金的进入会产生特殊的价格模式
3. **趋势确认**: 结合指数移动平均来确认趋势方向

## 使用建议

1. **参数调整**: 可以根据不同市场调整SMA的长度和乘数参数
2. **组合使用**: 建议与其他技术指标组合使用，提高信号准确性
3. **风险控制**: 设置止损位，控制单笔交易风险
4. **回测验证**: 在实际使用前进行充分的历史数据回测

## 注意事项

1. **数据质量**: 确保输入的OHLCV数据准确完整
2. **计算周期**: 指标需要至少30个交易日的数据才能稳定
3. **市场适应性**: 不同市场环境下指标的有效性可能不同
4. **延迟性**: 作为技术指标，存在一定的滞后性

## 示例输出

```
=== 回测结果 ===
初始资金: $100,000.00
最终价值: $117,588.71
总收益率: 17.59%
最大回撤: 15.66%
交易次数: 22

最近10天的指标和信号:
      date      close  BIG_MONEY1 SIGNAL  SIGNAL_STRENGTH
2023-12-20 144.97       0.17   HOLD             0.52
2023-12-21 147.96       0.09   HOLD             0.26
2023-12-22 143.47       0.04   HOLD             0.13
```

## 扩展功能

可以考虑添加的功能：

1. **实时数据接入**: 连接实时行情数据源
2. **多时间框架**: 支持不同时间周期的分析
3. **参数优化**: 自动寻找最优参数组合
4. **风险指标**: 添加更多风险评估指标
5. **可视化**: 增强图表显示功能

## 技术支持

如有问题或建议，请查看代码注释或运行测试文件进行调试。

---

**免责声明**: 本指标仅供学习和研究使用，不构成投资建议。实际交易中请谨慎使用并做好风险控制。
