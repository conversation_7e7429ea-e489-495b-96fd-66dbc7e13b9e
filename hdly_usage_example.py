"""
HDLYU3 指标使用示例
展示如何在实际交易策略中使用该指标
"""

import pandas as pd
import numpy as np
from hdly_indicator import HDLYIndicator


def load_stock_data(symbol="AAPL", start_date="2023-01-01", end_date="2023-12-31"):
    """
    加载股票数据 (示例函数，实际使用时需要连接真实数据源)
    
    Args:
        symbol: 股票代码
        start_date: 开始日期
        end_date: 结束日期
        
    Returns:
        包含OHLCV数据的DataFrame
    """
    # 这里使用模拟数据，实际使用时可以替换为：
    # - yfinance: import yfinance as yf; yf.download(symbol, start=start_date, end=end_date)
    # - tushare: import tushare as ts; ts.get_hist_data(symbol)
    # - 其他数据源API
    
    print(f"加载 {symbol} 从 {start_date} 到 {end_date} 的数据...")
    
    # 生成模拟数据
    dates = pd.date_range(start_date, end_date, freq='D')
    dates = [d for d in dates if d.weekday() < 5]  # 只保留工作日
    
    np.random.seed(42)
    n_days = len(dates)
    
    # 模拟股价走势
    base_price = 150
    returns = np.random.randn(n_days) * 0.02  # 2%日波动
    prices = [base_price]
    
    for ret in returns[1:]:
        new_price = prices[-1] * (1 + ret)
        prices.append(max(new_price, 1))
    
    prices = np.array(prices)
    
    # 生成OHLCV数据
    high_prices = prices * (1 + np.random.rand(n_days) * 0.025)
    low_prices = prices * (1 - np.random.rand(n_days) * 0.025)
    open_prices = prices + np.random.randn(n_days) * 1
    volumes = np.random.randint(1000000, 5000000, n_days)
    
    # 确保OHLC逻辑正确
    for i in range(n_days):
        high_prices[i] = max(high_prices[i], prices[i], open_prices[i])
        low_prices[i] = min(low_prices[i], prices[i], open_prices[i])
    
    df = pd.DataFrame({
        'date': dates,
        'open': open_prices,
        'high': high_prices,
        'low': low_prices,
        'close': prices,
        'volume': volumes
    })
    
    return df


def analyze_big_money_signals(df_with_indicator):
    """
    分析大资金信号
    
    Args:
        df_with_indicator: 包含指标的DataFrame
        
    Returns:
        包含信号分析的DataFrame
    """
    df = df_with_indicator.copy()
    
    # 定义信号
    df['SIGNAL'] = 'HOLD'
    df['SIGNAL_STRENGTH'] = 0
    
    # 大资金流入信号 (BIG_MONEY1 > 阈值且呈上升趋势)
    big_money_threshold = df['BIG_MONEY1'].quantile(0.8)  # 80分位数作为阈值
    
    buy_condition = (
        (df['BIG_MONEY1'] > big_money_threshold) &
        (df['BIG_MONEY1'] > df['BIG_MONEY1'].shift(1)) &
        (df['COLOR_CONDITION'] == True)
    )
    
    # 大资金流出信号
    sell_condition = (
        (df['BIG_MONEY1'] < df['BIG_MONEY1'].quantile(0.2)) &
        (df['BIG_MONEY1'] < df['BIG_MONEY1'].shift(1)) &
        (df['COLOR_CONDITION'] == False)
    )
    
    df.loc[buy_condition, 'SIGNAL'] = 'BUY'
    df.loc[sell_condition, 'SIGNAL'] = 'SELL'
    
    # 计算信号强度 (0-100)
    df['SIGNAL_STRENGTH'] = (df['BIG_MONEY1'] / df['BIG_MONEY1'].max() * 100).round(2)
    
    return df


def backtest_strategy(df_with_signals, initial_capital=100000):
    """
    简单的回测策略
    
    Args:
        df_with_signals: 包含信号的DataFrame
        initial_capital: 初始资金
        
    Returns:
        回测结果
    """
    df = df_with_signals.copy()
    
    # 初始化
    capital = initial_capital
    position = 0  # 持仓数量
    cash = capital
    portfolio_value = []
    trades = []
    
    for i, row in df.iterrows():
        current_price = row['close']
        signal = row['SIGNAL']
        
        # 计算当前组合价值
        current_value = cash + position * current_price
        portfolio_value.append(current_value)
        
        # 执行交易
        if signal == 'BUY' and cash > current_price:
            # 买入 (使用50%的现金)
            buy_amount = int((cash * 0.5) // current_price)
            if buy_amount > 0:
                position += buy_amount
                cash -= buy_amount * current_price
                trades.append({
                    'date': row['date'],
                    'action': 'BUY',
                    'price': current_price,
                    'quantity': buy_amount,
                    'value': buy_amount * current_price
                })
        
        elif signal == 'SELL' and position > 0:
            # 卖出 (卖出50%的持仓)
            sell_amount = position // 2
            if sell_amount > 0:
                position -= sell_amount
                cash += sell_amount * current_price
                trades.append({
                    'date': row['date'],
                    'action': 'SELL',
                    'price': current_price,
                    'quantity': sell_amount,
                    'value': sell_amount * current_price
                })
    
    df['PORTFOLIO_VALUE'] = portfolio_value
    
    # 计算回测指标
    final_value = portfolio_value[-1]
    total_return = (final_value - initial_capital) / initial_capital * 100
    
    # 计算最大回撤
    peak = np.maximum.accumulate(portfolio_value)
    drawdown = (peak - portfolio_value) / peak * 100
    max_drawdown = np.max(drawdown)
    
    results = {
        'initial_capital': initial_capital,
        'final_value': final_value,
        'total_return': total_return,
        'max_drawdown': max_drawdown,
        'total_trades': len(trades),
        'trades': trades,
        'portfolio_values': portfolio_value
    }
    
    return df, results


def main():
    """主函数"""
    print("=== HDLYU3 指标交易策略示例 ===\n")
    
    # 1. 加载数据
    stock_data = load_stock_data("AAPL", "2023-01-01", "2023-12-31")
    print(f"数据加载完成，共 {len(stock_data)} 条记录\n")
    
    # 2. 计算指标
    print("计算HDLYU3指标...")
    indicator = HDLYIndicator()
    df_with_indicator = indicator.calculate(stock_data)
    print("指标计算完成\n")
    
    # 3. 分析信号
    print("分析大资金信号...")
    df_with_signals = analyze_big_money_signals(df_with_indicator)
    
    # 统计信号
    signal_counts = df_with_signals['SIGNAL'].value_counts()
    print("信号统计:")
    for signal, count in signal_counts.items():
        print(f"  {signal}: {count} 次")
    print()
    
    # 4. 回测策略
    print("执行回测...")
    df_final, backtest_results = backtest_strategy(df_with_signals)
    
    # 5. 显示结果
    print("=== 回测结果 ===")
    print(f"初始资金: ${backtest_results['initial_capital']:,.2f}")
    print(f"最终价值: ${backtest_results['final_value']:,.2f}")
    print(f"总收益率: {backtest_results['total_return']:.2f}%")
    print(f"最大回撤: {backtest_results['max_drawdown']:.2f}%")
    print(f"交易次数: {backtest_results['total_trades']}")
    
    # 显示最近的交易记录
    if backtest_results['trades']:
        print("\n最近5笔交易:")
        recent_trades = backtest_results['trades'][-5:]
        for trade in recent_trades:
            print(f"  {trade['date'].strftime('%Y-%m-%d')}: {trade['action']} "
                  f"{trade['quantity']} 股 @ ${trade['price']:.2f}")
    
    # 显示最近的指标值
    print("\n最近10天的指标和信号:")
    recent_data = df_final[['date', 'close', 'BIG_MONEY1', 'SIGNAL', 'SIGNAL_STRENGTH']].tail(10)
    print(recent_data.to_string(index=False))
    
    return df_final, backtest_results


if __name__ == "__main__":
    df_result, results = main()
