"""
HDLYU3 指标 Python 实现
基于 Pine Script 转换而来，用于分析大资金流向
"""

import pandas as pd
import numpy as np


class HDLYIndicator:
    """HDLYU3 指标实现类"""
    
    def __init__(self):
        pass
    
    @staticmethod
    def sma_custom(series, length, multiplier):
        """
        自定义SMA函数，对应Pine Script中的SMA函数
        
        Args:
            series: 输入序列
            length: 长度参数
            multiplier: 乘数参数
            
        Returns:
            计算后的SMA序列
        """
        result = pd.Series(index=series.index, dtype=float)
        
        for i in range(len(series)):
            if i == 0:
                result.iloc[i] = series.iloc[i]
            else:
                prev_value = result.iloc[i-1] if not pd.isna(result.iloc[i-1]) else 0
                result.iloc[i] = (multiplier * series.iloc[i] + (length - multiplier) * prev_value) / length
                
        return result
    
    @staticmethod
    def ema(series, period):
        """
        指数移动平均线
        
        Args:
            series: 输入序列
            period: 周期
            
        Returns:
            EMA序列
        """
        return series.ewm(span=period, adjust=False).mean()
    
    @staticmethod
    def rolling_min(series, period):
        """
        滚动最小值
        
        Args:
            series: 输入序列
            period: 周期
            
        Returns:
            滚动最小值序列
        """
        return series.rolling(window=period).min()
    
    @staticmethod
    def rolling_max(series, period):
        """
        滚动最大值
        
        Args:
            series: 输入序列
            period: 周期
            
        Returns:
            滚动最大值序列
        """
        return series.rolling(window=period).max()
    
    def calculate(self, df):
        """
        计算HDLYU3指标
        
        Args:
            df: 包含OHLCV数据的DataFrame，需要包含列：
                - 'open': 开盘价
                - 'high': 最高价
                - 'low': 最低价
                - 'close': 收盘价
                - 'volume': 成交量
                
        Returns:
            包含指标结果的DataFrame
        """
        # 确保输入数据包含必要的列
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        for col in required_columns:
            if col not in df.columns:
                raise ValueError(f"DataFrame必须包含列: {col}")
        
        # 创建结果DataFrame
        result = df.copy()
        
        # 计算VARB (前一日最低价)
        varb = df['low'].shift(1)
        
        # 计算VARC1和VARC2
        varc1 = self.sma_custom(np.abs(df['low'] - varb), 3, 1)
        varc2 = self.sma_custom(np.maximum(df['low'] - varb, 0), 3, 1)
        
        # 计算VARC
        varc = (varc1 / varc2) * 100
        # 处理除零情况
        varc = varc.fillna(0)
        varc = varc.replace([np.inf, -np.inf], 0)
        
        # 计算VARD
        vard = self.ema(varc * 10, 3)
        
        # 计算VARE (30日最低价)
        vare = self.rolling_min(df['low'], 30)
        
        # 计算VARF (VARD的30日最高值)
        varf = self.rolling_max(vard, 30)
        
        # 计算BIG_MONEY_COND
        big_money_cond = pd.Series(index=df.index, dtype=float)
        for i in range(len(df)):
            if df['low'].iloc[i] <= vare.iloc[i]:
                big_money_cond.iloc[i] = (vard.iloc[i] + varf.iloc[i] * 2) / 2
            else:
                big_money_cond.iloc[i] = 0
        
        # 计算BIG_MONEY
        big_money = self.ema(big_money_cond, 3) / 618
        
        # 计算BIG_MONEY1 (限制在100以内)
        big_money1 = np.minimum(big_money, 100)
        
        # 计算颜色条件 (用于可视化)
        color_condition = (big_money1 > -150) & (big_money1 >= big_money1.shift(1))
        
        # 添加结果到DataFrame
        result['VARB'] = varb
        result['VARC1'] = varc1
        result['VARC2'] = varc2
        result['VARC'] = varc
        result['VARD'] = vard
        result['VARE'] = vare
        result['VARF'] = varf
        result['BIG_MONEY_COND'] = big_money_cond
        result['BIG_MONEY'] = big_money
        result['BIG_MONEY1'] = big_money1
        result['COLOR_CONDITION'] = color_condition
        result['CANDLE_HIGH'] = big_money1 * 2
        result['CANDLE_LOW'] = big_money1 * 2
        
        return result


def example_usage():
    """示例用法"""
    # 创建示例数据
    dates = pd.date_range('2023-01-01', periods=100, freq='D')
    np.random.seed(42)
    
    # 生成模拟的OHLCV数据
    close_prices = 100 + np.cumsum(np.random.randn(100) * 0.5)
    high_prices = close_prices + np.random.rand(100) * 2
    low_prices = close_prices - np.random.rand(100) * 2
    open_prices = close_prices + np.random.randn(100) * 0.5
    volumes = np.random.randint(1000, 10000, 100)
    
    df = pd.DataFrame({
        'date': dates,
        'open': open_prices,
        'high': high_prices,
        'low': low_prices,
        'close': close_prices,
        'volume': volumes
    })
    
    # 计算指标
    indicator = HDLYIndicator()
    result = indicator.calculate(df)
    
    # 显示结果
    print("HDLYU3 指标计算结果:")
    print(result[['date', 'low', 'BIG_MONEY1', 'COLOR_CONDITION']].tail(10))
    
    return result


if __name__ == "__main__":
    example_usage()
